import React, { useCallback, useState } from 'react';
import { <PERSON><PERSON>, I18nManager, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useFocusEffect } from '@react-navigation/native';
import Modal from 'react-native-modal';
import Share from 'react-native-share';
import { CloseCircle, SuccessIcon } from '../../../assets/svgs/icons';
import { colors, fonts } from '../../../utils/theme';
import { HORIZONTAL_DIMENS, USER_TYPES, VERTICAL_DIMENS } from '../../../constants';
import { PrimaryButton } from '../../common';
import { useAppSelector } from '../../../redux/hooks';
import { getTenantCountry, getTenantCurrency } from '../../../redux/selectors';
import { useDispatch } from 'react-redux';
import { getOrderReport } from '../../../redux/apis/orders';

interface Props {
	isVisible: boolean;
	onCancel: (type: string) => void;
	orderId: string;
}

const OrderSuccessModal = ({ isVisible, onCancel, orderId }: Props) => {
	const { t } = useTranslation();
	const dispatch = useDispatch();
	const currency = useAppSelector(getTenantCurrency);
	const tenantCountry = useAppSelector(getTenantCountry);
	const currentRole = useAppSelector(state => state.auth.currentRole);
	const [loadingPDF, setLoadingPDF] = useState(false);

	// Close modal when user navigates away from the current screen
	useFocusEffect(
		useCallback(() => {
			// This effect runs when the screen gains focus
			// The cleanup function runs when the screen loses focus
			return () => {
				if (isVisible) {
					onClose(); // Close the modal when navigating away
				}
			};
		}, [isVisible])
	);

	const onShare = async () => {
		setLoadingPDF(true);
		const requestBody = {
			reportType: 'ORDER_DETAIL',
			reportData: {
				orderId,
				tenantId: currentRole?.tenant_id?._id,
				currency,
				timezone: tenantCountry.timezone,
				apiVersion: 2
			}
		};
		const response = await dispatch(getOrderReport(requestBody));
		setLoadingPDF(false);
		if (!response.error) {
			// console.log('response', response.payload);
			setTimeout(async () => {
				const base64 = `data:application/pdf;base64,${response.payload.data}`;
				try {
					Share.open({
						title: 'Order details',
						url: base64,
						type: 'application/pdf',
						filename: 'order.pdf'
					}).then(() => {
						onClose();
					}).catch(() => {
						onClose();
					});
				} catch {
					onClose();
				}
			}, 400);
		} else {
			Alert.alert(t(response.payload.message));
		}
	};

	const onClose = () => {
		onCancel('close');
	};

	const onHomePress = () => {
		onCancel('home');
	};

	return (
		<Modal
			isVisible={isVisible}
			style={styles.container}
			animationIn="zoomIn"
			animationOut="zoomOut"
			backdropTransitionOutTiming={0}
			onBackdropPress={onClose} // Close modal when clicking outside
		>
			<View style={styles.modalContent}>
				<TouchableOpacity
					style={styles.closeButton}
					onPress={onClose}
					disabled={loadingPDF}
				>
					<CloseCircle />
				</TouchableOpacity>
				<Text style={styles.header}>{t('order_sent')}</Text>
				<Text style={styles.description}>{t('order_sent_success')}</Text>
				<SuccessIcon fill={colors.secondary} />
				{
					currentRole.portal_type !== USER_TYPES.CUSTOMER_APP && <PrimaryButton
						title={t('share_pdf')}
						onPress={onShare}
						loading={loadingPDF}
						style={styles.shareBtn}
					/>
				}
				<TouchableOpacity onPress={onHomePress} style={styles.homeBtn}>
					<Text style={styles.homeBtnText}>{t('home')}</Text>
				</TouchableOpacity>
			</View>
		</Modal>
	);
};

const styles = StyleSheet.create({
	container: {
		alignItems: 'center',
		justifyContent: 'center'
	},
	modalContent: {
		alignItems: 'center',
		backgroundColor: colors.white,
		borderRadius: 16,
		paddingVertical: VERTICAL_DIMENS._30,
		paddingHorizontal: 0,
		//height: VERTICAL_DIMENS._400,
		width: HORIZONTAL_DIMENS._315
	},
	closeButton: {
		position: 'absolute',
		top: 16,
		right: 16
	},
	header: {
		color: colors.primary,
		fontFamily: fonts.Montserrat.SemiBold,
		fontWeight: '600',
		fontSize: HORIZONTAL_DIMENS._24,
		marginTop: 18,
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	},
	description: {
		color: colors.primary,
		fontFamily: fonts.Montserrat.Regular,
		fontWeight: '400',
		fontSize: HORIZONTAL_DIMENS._14,
		marginTop: 16,
		marginBottom: 34,
		textAlign: 'center',
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	},
	shareBtn: {
		marginTop: VERTICAL_DIMENS._36,
		width: '80%'
	},
	homeBtn: {},
	homeBtnText: {
		color: colors.primary,
		fontFamily: fonts.Montserrat.Bold,
		fontWeight: '700',
		fontSize: HORIZONTAL_DIMENS._14,
		marginTop: VERTICAL_DIMENS._16,
		marginBottom: VERTICAL_DIMENS._10
	}
});

export { OrderSuccessModal };