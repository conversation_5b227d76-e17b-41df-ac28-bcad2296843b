import React, { useCallback, useEffect, useLayoutEffect, useRef, useState } from 'react';
import { Alert, I18nManager, Platform, ScrollView, Text, TouchableOpacity, View, TextInput, ActivityIndicator, FlatList, SectionList, StatusBar } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import Modal from 'react-native-modal';
import BottomSheet, { BottomSheetBackdrop, BottomSheetBackdropProps, BottomSheetView } from '@gorhom/bottom-sheet';
import ReactNativeModal from 'react-native-modal';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { getBuildNumber, getVersion } from 'react-native-device-info';
import { useDispatch } from 'react-redux';
import { setSelectedCustomerForPayment, setTransactionList } from '../../redux/features/payment-slice';
import { customerNumberVerify, getAllCustomers, getCustomerDetails } from '../../redux/apis/customer';
import { getTenantDetails, walletDetail } from '../../redux/apis/payment';
import { getSAPFlag } from '../../redux/apis/statement';
import { setCustomerGPSCoordinates, setCustomerVerified } from '../../redux/features/customer-slice';
import { setStatements } from '../../redux/features/statement-slice';
import { useAppSelector } from '../../redux/hooks';
import { getCustomerExternalId, getCustomerUserRoleId, getLanguages, getSalesPersonId, getTenantCurrency, getUsername, getUserType } from '../../redux/selectors';
import { VerifyNumberMobile } from '../../components/modals';
import { WalletLoader } from '../../components/loader-mobile';
import { FakeStatusBar, HeaderMobile, PriceDecimal, PrimaryButton } from '../../components/common';
import { AccountAction, AccountItem, ConfirmVerifyNumber, SelectLocation, ShiftPaymentSheet } from '../../components/mobile';
import { AccountRewardCard } from '../../components/mobile/rewards';
import { Bag, Briefcase, CheckMark, CloseCircle, CustomerProfile, Dismiss, Drafts, ForwardIcon, Language, LogOff, Money, NotificationBell, Profile, Search, Settings1, StarWithoutFill, Statements, TimeIcon } from '../../assets/svgs/icons';
import { HORIZONTAL_DIMENS, SAP_SERVICE, USER_TYPES, VERTICAL_DIMENS, _DEVICE_WIDTH } from '../../constants';
import { formatPhoneNumber } from '../../utils/libphonenumber';
import { colors } from '../../utils/theme';
import styles from './styles';
import { stopWatchLocation } from '../../utils/location';
import { checkBadRequest } from '../../utils/helpers';
import { trimText } from '../../utils/textTrim';
import { groupCustomersByAlphabet } from '../../utils/groupCustomersByAlphabet';
import useRewardProgram from '../../hooks/useRewardProgram';
import NotificationBadge from '../../components/mobile/NotificationCenter/NotificationBadge';
import { logout } from '../../redux/apis/auth';
import { getUserSetting as getUserSettingFromServer } from '../../redux/apis/sttings';
const Account = () => {
	const { t, i18n } = useTranslation();
	const navigation = useNavigation<any>();
	const paymentSheetRef = useRef<any>(null);
	const dispatch = useDispatch();
	const { bottom } = useSafeAreaInsets();
	const languages = useAppSelector(getLanguages);
	const userType = useAppSelector(getUserType);
	const userName = useAppSelector(getUsername);
	const salesPersonId = useAppSelector(getSalesPersonId);
	const currency = useAppSelector(getTenantCurrency);
	const customerExternalId = useAppSelector(getCustomerExternalId);
	const customerUserRoleId = useAppSelector(getCustomerUserRoleId);

	const userDetails = useAppSelector(state => state.auth.userDetails);
	const walletDetails = useAppSelector(state => state.payment.walletDetails);
	const loadingDetails = useAppSelector(state => state.payment.loadingDetails);
	const activeLanguage = languages.find(x => x.value === i18n.language);
	const activeActivity = useAppSelector(state => state.tracking.activeActivity);
	const activeShift = useAppSelector(state => state.tracking.activeShift);
	const isStartShiftVisible = useAppSelector(state => state.tracking.isStartShiftVisible);
	const { customers, loading } = useAppSelector(state => state.customer);
	const currentRole = useAppSelector(state => state.auth.currentRole);
	const tenantDetails = useAppSelector(state => state.payment.tenantDetails);
	const paymentService = tenantDetails?.services?.filter((e: any) => e?.key === 'payments');
	const isPaymentEnable = paymentService ? paymentService[0]?.permission?.edit : false;
	const selectedCustomerForPayment = useAppSelector(state => state.payment.selectedCustomerForPayment);
	const customerDetails = useAppSelector(state => state.customer.customerDetails);

	// const membershipType = useAppSelector((state) => state.reward.membershipType);

	const [searchString, setSearchString] = useState('');
	const [searchResults, setSearchResults] = useState<any[]>([]);
	const [showSearchResults, setShowSearchResults] = useState(false);
	const [verifyCustomer, setVerifyCustomer] = useState<any>(null);
	const [showVerifyConfirm, setShowVerifyConfirm] = useState(false);
	const [showLocationModal, setShowLocationModal] = useState(false);
	const [verifyLoader, setVerifyloader] = useState(false);
	const [showOTPModal, setShowOTPModal] = useState(false);
	const [selectedCustomerId, setSelectedCustomerId] = useState<string>('');
	const [showCustomerSelectModal, toggleCustomerSelectModal] = useState<boolean>(false);
	const [isSapEnable, setSapEnable] = useState<boolean>(false);

	useLayoutEffect(() => {
		navigation.setOptions({
			header: () => (
				<View>
					<FakeStatusBar />
					<HeaderMobile
						title={t('account')}
						bottomLine
						headerRight={
							<TouchableOpacity
								style={[styles.notifyContainer]}
								onPress={() => navigation.navigate('NotificationCenter')}
							>
								<View style={styles.badgeContainer}>
									<NotificationBadge />
								</View>
								<NotificationBell height={VERTICAL_DIMENS._24} width={VERTICAL_DIMENS._24} fill={colors.primary} />
							</TouchableOpacity>
						}
					/>
				</View>
			)
		});
	}, [navigation]);
	const SUPPLIER = [
		{ id: 1, name: 'شركة روائع الفرص التجارية' },
		{ id: 2, name: 'شركة روائع الفرص التجارية2' },
		{ id: 3, name: 'شركة روائع الفرص التجارية3' }
	];
	const bsRef = useRef<BottomSheet>(null);
	const [isModalVisible, setModalVisible] = useState(false);
	const [supplier, setSupplier] = useState('شركة روائع الفرص التجارية');

	// useFocusEffect(() => {
	// 	StatusBar.setBarStyle('dark-content');
	// 	return () => StatusBar.setBarStyle('light-content');
	// });
	const { allowToGetMemeberDetail, fetchRewardData } = useRewardProgram();

	useFocusEffect(useCallback(() => {
		if (allowToGetMemeberDetail) {
			fetchRewardData();
		}
	}, [allowToGetMemeberDetail]));

	useFocusEffect(useCallback(() => {
		getSAPService();
		getCustomerData();
		loadTenantDetail();
		dispatch(getUserSettingFromServer());
	}, []));

	const getSAPService = async () => {
		if (userType === USER_TYPES.CUSTOMER_APP) {
			const requestBody: any = {
				tenantId: currentRole?.tenant_id?._id,
				names: [SAP_SERVICE.SAP_SERVICE]
			};

			const response = await dispatch(getSAPFlag(requestBody));
			if (!response.error) {
				if (
					response?.payload?.data.length &&
					response?.payload?.data[0]?.is_active &&
					response?.payload?.data[0]?.configurations?.have_mobile_access
				) {
					setSapEnable(true);
				} else {
					setSapEnable(false);
				}
			} else {
				Alert.alert(t(checkBadRequest(response.payload)));
			}
		}
	};

	const getCustomerData = async () => {
		if (userType === USER_TYPES.CUSTOMER_APP) {
			await dispatch(getCustomerDetails({ userRoleId: customerUserRoleId }));
		}
	};

	const loadTenantDetail = async () => {
		await dispatch(getTenantDetails());
		isPaymentEnable && getWalletDetails();
	};

	const renderBackdrop = useCallback(
		(props_: BottomSheetBackdropProps) => (
			<BottomSheetBackdrop
				{...props_}
				pressBehavior="close"
				opacity={1.1}
				disappearsOnIndex={-1}
			/>
		),
		[]
	);

	useEffect(() => {
		setSearchString('');
		fetchCustomers();
	}, [showCustomerSelectModal]);

	useEffect(() => {
		if (selectedCustomerForPayment !== null) {
			setSelectedCustomerId(selectedCustomerForPayment._id); // Make checkbox selected if customer is selected already before
		};
	}, [selectedCustomerForPayment, showCustomerSelectModal]);

	const fetchCustomers = (searchKey?: string) => {
		// Load customer list
		const requestBody = {
			tenantId: currentRole?.tenant_id?._id,
			salesPersonId: salesPersonId,
			supervisorId: '',
			customerType: 'ALL',
			type: 'ALL',
			status: 'ACTIVE',
			sortByName: true,
			searchKey: ''
		};
		if (searchKey) requestBody.searchKey = searchKey;
		if (userType === USER_TYPES.SUPERVISOR_APP) {
			requestBody.supervisorId = currentRole.user_id;
		}
		dispatch(getAllCustomers(requestBody));
	};

	const getWalletDetails = async () => {
		const requestBody: any = {
			salesPersonId: salesPersonId
		};
		await dispatch(walletDetail(requestBody));
	};

	const onClose = () => {
		setModalVisible(!isModalVisible);
	};

	const onLogout = () => {
		if (activeActivity) {
			setModalVisible(false);
			Alert.alert(t('unable_logout'), t('logout_alert_msg'));
		} else {
			stopWatchLocation();
			dispatch(logout());
		}
	};

	const handleWallet = () => {
		const isWalletPresent = walletDetails?._id ? true : false;
		dispatch(setTransactionList([]));
		navigation.navigate('Transactions', { isWalletPresent });
	};

	const toggleSelectCustomer = () => {
		toggleCustomerSelectModal(!showCustomerSelectModal);
		if (showCustomerSelectModal === true) {
			setSearchString(''); // Clear search input on close
			setShowSearchResults(false); // Hide search results on close
			setSearchResults([]); // Clear search results on close
		}
	};

	const onSearch = (value: string) => {
		const showSearchResultsFlag = value.trim().length > 0;
		let searchText = trimText(value);
		setSearchString(searchText);
		setShowSearchResults(showSearchResultsFlag);
		const searchedValue = searchText.toLowerCase();
		// console.log('searchedValue', searchedValue);
		const filteredCustomers = customers.filter((x) => {
			// console.log('match', x.value.toLowerCase(), x.value.toLowerCase().includes(searchedValue));
			return x.value.toLowerCase().includes(searchedValue) || x.external_id?.toLowerCase()?.includes(searchedValue);
		});
		// console.log('filteredCustomers', filteredCustomers.length);
		setSearchResults(filteredCustomers);
	};

	const toggleLocationModal = () => {
		setShowLocationModal(!showLocationModal);
	};

	const onSelectCustomer = (item: any) => {
		// If customer is not verified then verify first
		if (!item?.is_verified) {
			setVerifyCustomer(item); // Set customer in state to use country code and mobile number in verify otp modal
			setShowVerifyConfirm(!showVerifyConfirm); // Display confirmation popup for verify number
			return;
		}

		// If gps location is not set then set first
		if (item.gps_coordinates?.latitude === 0 || item.gps_coordinates === undefined || item.gps_coordinates.longitude === 0) {
			toggleLocationModal();
			return;
		}

		if (item?.is_verified) {
			dispatch(setSelectedCustomerForPayment(item));
			toggleCustomerSelectModal(!showCustomerSelectModal);
			paymentSheetRef.current?.open();
			setSearchString('');
			setShowSearchResults(false);
			setSearchResults([]);
			return;
		}
	};

	const handleItem = (item: any) => {
		onSelectCustomer(item);
		setSelectedCustomerId(item.key);
	};

	const renderItem = ({ item }: any) => {
		return (
			<TouchableOpacity
				disabled={!item?.is_payment_enabled}
				style={styles.listItemContainer}
				onPress={() => handleItem(item)}
			>
				<View style={styles.nameContainer}>
					<Text style={styles.listItemLabel} numberOfLines={2} ellipsizeMode='tail'>{item.value}</Text>
					{
						item?.external_id && <Text style={styles.externalId}>{item?.external_id}</Text>
					}
				</View>
				{!item?.is_payment_enabled &&
					<Text style={styles.paymentDisable}>{t('payment_disabled')}</Text>
				}
			</TouchableOpacity>
		);
	};

	const handleCancel = () => {
		setShowVerifyConfirm(!showVerifyConfirm);
	};

	const verifyMobileNumber = async (selectedCustomer: any) => {
		let number = selectedCustomer?.customer_mobile_number;
		const result = number?.toString().startsWith('0');
		result === true ? number = number.substring(1) : number;
		//console.log('selectedCustomer in verify mobile number', selectedCustomer);
		const requestBody = {
			action: 'SEND_OTP',
			mobileNumber: number,
			countryCode: selectedCustomer?.customer_country_code
		};
		const response = await dispatch(customerNumberVerify(requestBody));
		setVerifyloader(false);
		if (!response.error) {
			setShowVerifyConfirm(false); // Close confirmation modal
			setTimeout(() => {
				setShowOTPModal(true); // Show enter otp modal
			}, 400);
		} else {
			Alert.alert(t(checkBadRequest(response.payload)));
		}
	};

	const handleVerify = () => {
		// setShowVerifyConfirm(!showVerifyConfirm);
		setVerifyloader(true);
		verifyMobileNumber(verifyCustomer); // Send verification otp
	};

	const handleEdit = () => {
		handleCancel();
		toggleCustomerSelectModal(!showCustomerSelectModal);
		navigation.navigate('UpdateCustomer', { id: selectedCustomerId, edit: true });
	};

	const onToggleOtpModal = () => {
		setShowOTPModal(!showOTPModal);
	};

	const onMobileVerifySuccess = () => {
		setShowOTPModal(false); // Close enter otp modal
		dispatch(setCustomerVerified(selectedCustomerId)); // Set is_verified true manually instead of API call
	};

	const onSelectLocation = (locationDetails: any) => {
		toggleLocationModal();
		dispatch(setCustomerGPSCoordinates({
			customerRoleId: selectedCustomerId,
			latitude: locationDetails.latitude,
			longitude: locationDetails.longitude
		}));
	};

	const handleStatement = () => {
		if (customerExternalId) {
			dispatch(setStatements([]));
			navigation.navigate('Statements');
		} else {
			Alert.alert('You do not have external id');
		}
	};
	useFocusEffect(() => {
		StatusBar.setBarStyle('dark-content', true);
	});

	return (
		<>
			<ScrollView bounces={false} showsVerticalScrollIndicator={false}>
				<View style={styles.mainContainer}>

					{userType !== USER_TYPES.CUSTOMER_APP &&
						<View style={styles.profileContainer}>
							<Profile style={styles.iconStyle} />
							<View style={styles.textContainer}>
								<Text style={styles.name} numberOfLines={2} ellipsizeMode='tail'>{userName}</Text>
								<Text style={styles.contact}>{formatPhoneNumber(userDetails.country_code, userDetails.mobile_number)}</Text>
							</View>
						</View>
					}

					{userType === USER_TYPES.CUSTOMER_APP &&
						<View style={[styles.profileContainer, styles.customerContainer]}>
							<CustomerProfile style={styles.iconStyle} />
							<View style={styles.textContainer}>
								<Text style={styles.legalName} numberOfLines={2} ellipsizeMode='tail'>{currentRole?.customer_legal_name}</Text>
								<Text style={styles.customerName} numberOfLines={2} ellipsizeMode='tail'>{userName}</Text>
								<Text style={styles.contact}>{formatPhoneNumber(userDetails.country_code, userDetails.mobile_number)}</Text>
							</View>
						</View>
					}

					<View style={styles.accounActionContainer}>
						<AccountAction
							icon={<Bag fill={colors.white} />}
							title={t('orders')}
							handleNavigation={() => navigation.navigate('Orders')}
							style={styles.accountActionItem}
						/>
						{userType === USER_TYPES.CUSTOMER_APP
							&& isSapEnable
							&& customerDetails?.is_payment_enabled
							&& isPaymentEnable
							&& <AccountAction
								icon={<Statements fill={colors.white} />}
								title={t('statements')}
								handleNavigation={handleStatement}
								style={styles.accountActionItem}
							/>
						}
						{
							userType !== USER_TYPES.CUSTOMER_APP && (
								<AccountAction
									icon={<Drafts fill={colors.white} />}
									title={t('drafts')}
									handleNavigation={() => navigation.navigate('Drafts')}
									style={styles.accountActionItem}
								/>
							)
						}
						{userType === USER_TYPES.SALES_APP
							&& isPaymentEnable
							&& !activeShift
							&& !activeActivity
							&& !isStartShiftVisible
							&&
							<AccountAction
								icon={<Money fill={colors.white} />}
								title={t('payment')}
								handleNavigation={toggleSelectCustomer}
								style={styles.accountActionItem}
							/>
						}
						<AccountAction
							icon={<StarWithoutFill stroke={colors.white} />}
							title={t('favorites')}
							handleNavigation={() => navigation.navigate('Favorites')}
							style={styles.accountActionItem}
						/>
					</View>
					{/* ********************************| Reward card |********************************  */}
					{userType === USER_TYPES.CUSTOMER_APP &&
						<AccountRewardCard />
					}
				</View>

				{userType === USER_TYPES.SALES_APP && isPaymentEnable &&
					<TouchableOpacity activeOpacity={0.8} onPress={handleWallet} style={[styles.accountItemContainer, { paddingHorizontal: HORIZONTAL_DIMENS._16, paddingVertical: VERTICAL_DIMENS._15 }]}>
						<Text style={styles.description}>{t('wallet')}</Text>
						<View style={styles.priceView}>
							<View style={styles.priceTxtView}>
								{loadingDetails ?
									<WalletLoader />
									: <>
										<PriceDecimal value={walletDetails?.balance ? walletDetails?.balance : 0} style={styles.price} showCurrency={false} />
										<Text style={styles.currnecy}>{currency}</Text>
									</>
								}
							</View>
							<ForwardIcon style={{ transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }] }} />
						</View>
						{/* <Text style={styles.pendingCheque}>{t('pending_cheque')}: 2</Text> */}
					</TouchableOpacity>
				}

				<View style={styles.accountItemContainer}>
					{/* <AccountItem
						leftIcon={<PinIcon />}
						title={t('address')}
						rightIcon={<ForwardIcon style={{ transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }] }} />}
					/>
					<AccountItem
						leftIcon={<ProfileIcon />}
						title={t('profile')}
						rightIcon={<ForwardIcon style={{ transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }] }} />}
						handleNavigation={() => navigation.navigate('Profile')}
					/> */}
					{
						userType !== USER_TYPES.CUSTOMER_APP && <AccountItem
							leftIcon={<Briefcase />}
							title={t('customers')}
							rightIcon={<ForwardIcon style={{ transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }] }} />}
							handleNavigation={() => navigation.navigate('Customers')}
						/>
					}
					{
						userType === USER_TYPES.SALES_APP && <AccountItem
							leftIcon={<TimeIcon />}
							title={t('timesheet')}
							rightIcon={<ForwardIcon style={{ transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }] }} />}
							handleNavigation={() => navigation.navigate('Timesheet')}
						/>
					}
					{
						userType !== USER_TYPES.CUSTOMER_APP && <AccountItem
							leftIcon={<Settings1 />}
							title={t('settings')}
							rightIcon={<ForwardIcon style={{ transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }] }} />}
							handleNavigation={() => navigation.navigate('Settings')}
						/>
					}
					{/* <AccountItem
						leftIcon={<Notifications />}
						title={t('notifications')}
						rightIcon={
							<ForwardIcon style={{ transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }] }} />
						}
						handleNavigation={() => navigation.navigate('Notifications')}
					/> */}
					<AccountItem
						leftIcon={<Language />}
						title={t('language')}
						rightIcon={<ForwardIcon style={{ transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }] }} />}
						rightTitle={activeLanguage?.name}
						handleNavigation={() => navigation.navigate('Language')}
					/>
				</View>

				<View style={styles.accountItemContainer}>
					{/* <AccountItem
						leftIcon={<SwitchIcon />}
						title={t('switch-suppliers')}
						handleNavigation={() => bsRef.current?.expand()}
					/> */}
					<AccountItem
						leftIcon={<LogOff />}
						title={t('logoff')}
						handleNavigation={() => setModalVisible(true)}
					/>
				</View>
				<View style={styles.appVersionContainer}>
					<Text style={styles.appVersionText}>{t('app_version')} {`${getVersion()} (${getBuildNumber()})`}</Text>
				</View>
			</ScrollView>

			<ReactNativeModal
				isVisible={isModalVisible}
				avoidKeyboard={Platform.OS === 'ios'}
				backdropTransitionOutTiming={0}
				animationIn="zoomIn"
				animationOut="zoomOut"
			>
				<View style={styles.modalContainer}>
					<TouchableOpacity onPress={onClose}>
						<Dismiss style={styles.dismissButton} />
					</TouchableOpacity>
					<Text style={styles.logoutTitle}>{t('logout')}</Text>
					<Text style={styles.confirmTitle}>{t('confirm_logout')}</Text>
					<LogOff style={styles.icon} height={VERTICAL_DIMENS._64} width={HORIZONTAL_DIMENS._64} />
					<View style={styles.bottomContainer}>
						<TouchableOpacity style={styles.cancelContainer} onPress={onClose}>
							<Text style={styles.cancelTitle}>{t('cancel')}</Text>
						</TouchableOpacity>
						<View style={styles.buttonContainer}>
							<PrimaryButton
								title={t('logout')}
								titleStyle={styles.titleStyle}
								style={{ width: HORIZONTAL_DIMENS._128 }}
								onPress={onLogout}
							/>
						</View>
					</View>
				</View>
			</ReactNativeModal>

			<BottomSheet
				ref={bsRef}
				index={-1}
				backdropComponent={renderBackdrop}
				animateOnMount={true}
				handleIndicatorStyle={styles.bottomSheetIndicator}
				enablePanDownToClose={true}
				enableDynamicSizing={true}
			>
				<BottomSheetView
					style={[styles.bottomSheet, bottom > 0 && { paddingBottom: bottom - VERTICAL_DIMENS._8 }, Platform.OS === 'android' && { paddingBottom: VERTICAL_DIMENS._20 }]}
				>
					<Text style={styles.title}>{t('switch_supplier')}</Text>
					<View style={{ marginTop: VERTICAL_DIMENS._16, backgroundColor: colors.white, width: _DEVICE_WIDTH }}>
						{
							SUPPLIER.map((item) => {
								return (
									<TouchableOpacity
										style={styles.languageContainer}
										onPress={() => setSupplier(item.name)}
										key={item.id}
									>
										<Text style={styles.languageContentTitle}>{item.name}</Text>
										{supplier === item.name ?
											<CheckMark
												fill={colors.primary}
											/> : <CheckMark
												fill={colors.white}
											/>}
									</TouchableOpacity>
								);
							})
						}
					</View>
				</BottomSheetView>
			</BottomSheet>

			<Modal
				isVisible={showCustomerSelectModal}
				style={styles.customerModalContainer}
			>
				<View style={styles.modalContent}>
					<View style={styles.modalHeader}>
						<Text style={styles.selectCustomerText}>{t('payment_collection')}</Text>
						<TouchableOpacity onPress={toggleSelectCustomer} style={styles.closeBtn}>
							<CloseCircle />
						</TouchableOpacity>
					</View>
					<View style={styles.searchInputContainer}>
						<TextInput
							placeholder={t('search')}
							style={styles.searchInput}
							value={searchString}
							onChangeText={onSearch}
						/>
						<View style={styles.searchIconContainer}>
							<Search height={20} width={20} fill={colors.grey600} />
						</View>
					</View>
					<View style={styles.customerListView}>
						{
							loading && <View style={styles.loadingContainer}>
								<ActivityIndicator color={colors.primary} />
							</View>
						}
						{
							!loading && !showSearchResults &&

							// <AlphabetList
							// 	data={customers}
							// 	style={styles.listContainer}
							// 	indexContainerStyle={styles.alphabetContainer}
							// 	indexLetterStyle={styles.indexLetterStyle}
							// 	renderCustomItem={renderItem}
							// 	renderCustomSectionHeader={(section) => (
							// 		<View style={styles.sectionHeaderContainer}>
							// 			<Text style={styles.sectionHeaderLabel}>{section.title}</Text>
							// 		</View>
							// 	)}
							// 	showsVerticalScrollIndicator={false}
							// 	ListEmptyComponent={() => <View style={styles.emptyContainer}>
							// 		<Text style={styles.noRecordsText}>{t('no_result_found')}</Text>
							// 	</View>}
							// 	initialNumToRender={20}
							// />
							<SectionList
								sections={groupCustomersByAlphabet([...customers])}
								keyExtractor={(item) => item._id.toString()}
								renderItem={renderItem}
								style={styles.listContainer}
								renderSectionHeader={({ section }) => (
									<View style={styles.sectionHeaderContainer}>
										<Text style={styles.sectionHeaderLabel}>{section.title}</Text>
									</View>
								)}
								ListEmptyComponent={() => <View style={styles.emptyContainer}>
									<Text style={styles.noRecordsText}>{t('no_result_found')}</Text>
								</View>}
								showsVerticalScrollIndicator={false}
							// ItemSeparatorComponent={() => <View style={styles.separator} />}
							/>
						}
						{
							showSearchResults && <FlatList
								data={searchResults}
								style={styles.listContainer}
								renderItem={({ item }: any) => (
									<TouchableOpacity
										disabled={!item?.is_payment_enabled}
										style={styles.listItemContainer}
										onPress={() => handleItem(item)}
									>
										<View style={styles.nameContainer}>
											<Text style={styles.listItemLabel} numberOfLines={2} ellipsizeMode='tail'>{item.value}</Text>
											{
												item?.external_id && <Text style={styles.externalId}>{item?.external_id}</Text>
											}
										</View>
										{!item?.is_payment_enabled &&
											<Text style={styles.paymentDisable}>{t('payment_disabled')}</Text>
										}
									</TouchableOpacity>
								)}
								ListEmptyComponent={() => <View style={styles.emptyContainer}>
									<Text style={styles.noRecordsText}>{t('no_result_found')}</Text>
								</View>}
							/>
						}
					</View>
					<ConfirmVerifyNumber
						heading={t('verify_mobile')}
						title={t('verify_number_confirm')}
						confirmButtonTitle={t('send_otp')}
						countryCode={verifyCustomer?.customer_country_code}
						mobileNumber={verifyCustomer?.customer_mobile_number}
						isVisible={showVerifyConfirm}
						onCancel={handleCancel}
						onConfirm={handleVerify}
						onEdit={handleEdit}
						loader={verifyLoader}
					/>
					<VerifyNumberMobile
						countryCode={verifyCustomer?.customer_country_code}
						mobileNumber={verifyCustomer?.customer_mobile_number}
						isVisible={showOTPModal}
						selectedCustomerId={selectedCustomerId}
						onCancel={onToggleOtpModal}
						onVerified={onMobileVerifySuccess}
					/>
					<SelectLocation
						isVisible={showLocationModal}
						onClose={toggleLocationModal}
						onSelect={onSelectLocation}
						customerRoleId={selectedCustomerId}
					/>
				</View>
			</Modal>

			<ShiftPaymentSheet ref={paymentSheetRef} />
		</>
	);
};

export default Account;
