import { createAsyncThunk } from '@reduxjs/toolkit';
import httpService from './httpService';
import { RootState } from '../store';

export const getCategory: any = createAsyncThunk<any>(
	'getCategory', async (data: any, { rejectWithValue }) => {
		try {
			data.productSchemaType = 'products_2.0';
			const response = await httpService.post('/productService/category/get-category', data);
			// console.log('response category', response.data);
			return response.data;
		} catch (error: any) {
			console.log('error in getCategory', error.response.data);
			return rejectWithValue(error.response.data);
		}
	}
);

export const getCategoryProducts: any = createAsyncThunk<any>(
	'getCategoryProducts', async (data: any, { rejectWithValue, getState }) => {
		try {
			const { auth } = getState() as RootState;
			const params: any = Object.assign({}, data);
			delete params.index; // delete properties which sent for redux action usage
			delete params.requestId; // delete properties which sent for redux action usage

			params.productSchemaType = 'products_2.0';

			const accessToken = auth.token.accessToken;
			if (!accessToken) {
				return rejectWithValue('No access token');
			}

			const userRoleId = auth.currentRole?._id;
			if (!userRoleId) {
				return rejectWithValue('No user role id');
			}

			params.isProductSplitting = true;
			const response = await httpService.get('/productService/category/categoryProductList', {
				params: params
			});
			return {
				...response.data,
				body: data
			};
		} catch (error: any) {
			console.log('error in getCategoryProducts', error.response.data);
			return rejectWithValue(error.response.data);
		}
	}
);

export const getSuggestedProductsByCategory: any = createAsyncThunk<any>(
	'getSuggestedProductsByCategory', async (data: any, { rejectWithValue }) => {
		try {
			data.productSchemaType = 'products_2.0';
			data.isProductSplitting = true;
			const response = await httpService.get('/productService/category/categoryProductList', {
				params: data
			});
			return {
				...response.data,
				body: data
			};
		} catch (error: any) {
			console.log('error in getCategoryProducts', error.response.data);
			return rejectWithValue(error.response.data);
		}
	}
);

export const getProductId: any = createAsyncThunk<any>(
	'getProductId', async (data, { rejectWithValue }) => {
		try {
			const response = await httpService.get('/productService/productV2/barcodeDetails', {
				params: data
			});
			//console.log('response of getProductId', response.data);
			return response.data;
		} catch (error: any) {
			console.log('error in getProductId', error.response.data);
			return rejectWithValue(error.response.data);
		}
	}
);

export const getProductDetails: any = createAsyncThunk<any>(
	'getProductDetails', async (data, { rejectWithValue }) => {
		try {
			const response = await httpService.get('/productService/productV2', {
				params: data
			});
			return response.data;
		} catch (error: any) {
			console.log('error in getProductDetails', error.response.data);
			return rejectWithValue(error.response.data);
		}
	}
);

export const getMasterTax: any = createAsyncThunk<any>(
	'getMasterTax', async (data, { rejectWithValue }) => {
		try {
			const response = await httpService.get('/productService/configuration/master-tax', {
				params: data
			});
			//console.log('response of getMasterTax', response.data);
			return response.data;
		} catch (error: any) {
			console.log('error in getMasterTax', error.response.data);
			return rejectWithValue(error.response.data);
		}
	}
);

export const getTaxById: any = createAsyncThunk<any>(
	'getTaxById', async (data, { rejectWithValue }) => {
		try {
			const response = await httpService.get('/productService/configuration/tax', {
				params: data
			});
			//console.log('response of getTaxById', response.data);
			return response.data;
		} catch (error: any) {
			console.log('error in getTaxById', error.response.data);
			return rejectWithValue(error.response.data);
		}
	}
);

export const getUnitList: any = createAsyncThunk<any>(
	'getUnitList', async (data, { rejectWithValue, getState }) => {
		try {
			const { auth } = getState() as RootState;
			const tenantId = auth.currentRole?.tenant_id?._id;
			const queryParams = {
				tenantId,
				status: 'ALL',
				searchKey: '',
				perPage: 50,
				page: 1
			};
			const response = await httpService.get('productService/masterData/unit', {
				params: queryParams
			});
			//console.log('response of unit list', response.data);
			return response.data;
		} catch (error: any) {
			console.log('error in getUnitList', error.response.data);
			return rejectWithValue(error.response.data);
		}

	}
);
export const getSearchProducts: any = createAsyncThunk<any>(
	'getSearchProducts', async (data: any, { rejectWithValue }) => {
		try {
			data.isProductSplitting = true;
			const response = await httpService.get('/productService/productV2/search', {
				params: data
			});
			return response.data;
		} catch (error: any) {
			console.log('error in getSearchProducts', error.response.data);
			return rejectWithValue(error.response.data);
		}
	}
);

export const getFilterProducts: any = createAsyncThunk<any>(
	'getFilterProducts', async (data: any, { rejectWithValue }) => {
		try {
			data.isProductSplitting = true;
			const response = await httpService.get('/productService/productV2/search', {
				params: data
			});
			return response.data;
		} catch (error: any) {
			console.log('error in getSearchProducts', error.response.data);
			return rejectWithValue(error.response.data);
		}
	}
);

export const getNewProducts: any = createAsyncThunk<any>(
	'getNewProducts', async (data: any, { rejectWithValue }) => {
		//console.log('params of new products', data);
		try {
			const filter = encodeURIComponent(JSON.stringify(data.filters));
			delete data.filters;
			data.isProductSplitting = true;
			const response = await httpService.get(`/productService/productV2/search?filters=${filter}`, {
				params: data
			});
			// console.log('response of new products', response);
			return response.data;
		} catch (error: any) {
			console.log('error in getNewProducts', error.response.data);
			return rejectWithValue(error.response.data);
		}
	}
);

export const getRestockedProducts: any = createAsyncThunk<any>(
	'getRestockedProducts', async (data: any, { rejectWithValue }) => {
		//console.log('params of restocked products', data);
		try {
			const filter = encodeURIComponent(JSON.stringify(data.filters));
			delete data.filters;
			data.isProductSplitting = true;
			const response = await httpService.get(`/productService/productV2/search?filters=${filter}`, {
				params: data
			});
			// console.log('response of restocked products', response);
			return response.data;
		} catch (error: any) {
			console.log('error in getRestockedProducts', error.response.data);
			return rejectWithValue(error.response.data);
		}
	}
);

export const addToFavoriteProduct: any = createAsyncThunk<any>(
	'addToFavoriteProduct', async (data, { rejectWithValue }) => {
		try {
			const response = await httpService.post('/productService/productV2/favoriteProduct', data);
			return response.data;
		} catch (error: any) {
			console.log('error in addToFavoriteProduct', error.response.data);
			return rejectWithValue(error.response.data);
		}
	}
);

export const addAllProductToFavorite: any = createAsyncThunk<any>(
	'addAllProductToFavorite', async (data, { rejectWithValue }) => {
		try {
			const response = await httpService.post('/productService/productV2/favoriteProduct', data);
			//console.log('favourite respone', response.data);
			return response.data;
		} catch (error: any) {
			console.log('error in addAllProductToFavorite', error.response.data);
			return rejectWithValue(error.response.data);
		}
	}
);

export const getFavoriteProducts: any = createAsyncThunk<any>(
	'getFavoriteProducts', async (data, { rejectWithValue }) => {
		try {
			const response = await httpService.get('/productService/productV2/favoriteProduct', {
				params: data
			});
			return response.data;
		} catch (error: any) {
			console.log('error in getFavoriteProducts', error.response.data);
			return rejectWithValue(error.response.data);
		}
	}
);

export const getProductFilterCategory: any = createAsyncThunk<any>(
	'getProductFilterCategory', async (data: any, { rejectWithValue }) => {
		try {
			data.productSchemaType = 'products_2.0';
			const response = await httpService.post('/productService/category/get-category', data);
			//console.log('response category', response.data);
			return response.data;
		} catch (error: any) {
			console.log('error in getProductFilterCategory', error.response.data);
			return rejectWithValue(error.response.data);
		}
	}
);

export const getFilterCategoryProducts: any = createAsyncThunk<any>(
	'getFilterCategoryProducts', async (data: any, { rejectWithValue }) => {
		try {
			const params: any = Object.assign({}, data);
			delete params.index; // delete properties which sent for redux action usage
			delete params.requestId; // delete properties which sent for redux action usage

			params.productSchemaType = 'products_2.0';
			const filters = encodeURIComponent(JSON.stringify(params.filters));
			delete params.filters;
			params.isProductSplitting = true;
			const response = await httpService.get(`/productService/category/categoryProductList?filters=${filters}`, {
				params: params
			});
			//console.log('response of product list', response.data.data.list);
			return {
				...response.data,
				body: data
			};
		} catch (error: any) {
			console.log('error in getFilterCategoryProducts', error.response.data);
			return rejectWithValue(error.response.data);
		}
	}
);